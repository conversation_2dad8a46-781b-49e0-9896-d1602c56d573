// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";
import { browserTracingIntegration } from "@sentry/browser";

// 只在生产环境中初始化 Sentry
if (process.env.NODE_ENV === "production") {
  console.log("Initializing Sentry in production mode");

  Sentry.init({
    dsn: "https://<EMAIL>/4508829166993408",

    // 使用 browserTracingIntegration，但限制自动追踪
    integrations: [
      browserTracingIntegration({
        // 禁用自动页面导航追踪，减少噪音
        enableLongTask: false,
        enableInp: false,
        // 只追踪重要的交互
        tracePropagationTargets: [
          "localhost",
          /^https:\/\/api\.uniscribe\.co/,
          /^https:\/\/api-surge\.uniscribe\.co/,
        ],
      }),
    ],

    // 降低性能监控采样率，减少数据量
    tracesSampleRate: 0.1, // 从 100% 降到 10%

    // Session replay 配置保持不变
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,

    // 启用错误上报
    enabled: true,

    // 设置环境
    environment: "production",

    // 如果需要调试，可以临时打开
    debug: false,

    // 保持错误采样率 100%，确保捕获所有真正的错误
    sampleRate: 1.0, // 保持 100%，通过 ignoreErrors 和 beforeSend 过滤噪音

    // 扩展忽略错误列表，过滤更多噪音
    ignoreErrors: [
      // 忽略网络错误
      "Network request failed",
      "NetworkError",
      "Failed to fetch",
      // 忽略浏览器扩展错误（消息文本很少包含 URL，真正的过滤在 denyUrls 和 beforeSend 中完成）
      // 下面这些正则在 ignoreErrors 中通常不会生效，但保留以防个别扩展将 scheme 写入 message
      /^chrome-extension:\/\//,
      /^safari-extension:\/\//,
      /^moz-extension:\/\//,
      // 忽略扩展服务工作者错误
      /extensionServiceWorker\.js/,
      // 忽略TON钱包扩展相关错误
      /Cannot read properties of undefined \(reading 'ton'\)/,
      /requestAccounts.*ton/,
      /onDappSendUpdates.*ton/,
      // 忽略其他常见的加密货币钱包扩展错误
      /Cannot read properties of undefined \(reading 'ethereum'\)/,
      /Cannot read properties of undefined \(reading 'solana'\)/,
      /Cannot read properties of undefined \(reading 'bitcoin'\)/,
      // 忽略常见的用户取消操作
      "AbortError",
      "Operation cancelled",
      // 忽略 ResizeObserver 错误（常见但无害）
      "ResizeObserver loop limit exceeded",
      // 忽略非关键的 DOM 错误
      "Non-Error promise rejection captured",
      // 忽略第三方脚本错误
      /Script error/,
    ],

    // 从这些来源来的脚本直接丢弃（更可靠地过滤浏览器扩展/ServiceWorker）
    denyUrls: [
      /chrome-extension:\/\//,
      /safari-extension:\/\//,
      /moz-extension:\/\//,
      /extensionServiceWorker\.js/,
    ],

    // 添加 beforeSend 钩子，进一步过滤不重要的错误
    beforeSend(event, hint) {
      // 过滤掉开发工具相关的错误
      if (event.exception) {
        const error = hint?.originalException;
        if (
          error &&
          error.stack &&
          error.stack.includes("chrome-devtools://")
        ) {
          return null;
        }
      }

      // 过滤来自浏览器扩展/扩展 Service Worker 的错误
      const frames = event?.exception?.values?.[0]?.stacktrace?.frames ?? [];
      if (
        frames.some((f) => {
          const file = f?.filename || "";
          return (
            file.includes("chrome-extension://") ||
            file.includes("safari-extension://") ||
            file.includes("moz-extension://") ||
            file.includes("extensionServiceWorker.js")
          );
        })
      ) {
        return null;
      }

      // 过滤掉频繁的网络超时错误
      if (event.message && event.message.includes("timeout")) {
        return null;
      }

      // 过滤掉浏览器扩展相关的错误
      if (event.exception && event.exception.values) {
        for (const exception of event.exception.values) {
          if (exception.stacktrace && exception.stacktrace.frames) {
            // 检查是否有来自扩展的堆栈帧
            const hasExtensionFrame = exception.stacktrace.frames.some(frame => {
              if (!frame.filename) return false;
              
              return (
                // 扩展服务工作者
                frame.filename.includes('extensionServiceWorker.js') ||
                // 各种扩展协议
                frame.filename.startsWith('chrome-extension://') ||
                frame.filename.startsWith('safari-extension://') ||
                frame.filename.startsWith('moz-extension://') ||
                frame.filename.startsWith('app:///extensionServiceWorker.js') ||
                // 内容脚本
                frame.filename.includes('contentScript') ||
                frame.filename.includes('injected.js')
              );
            });

            if (hasExtensionFrame) {
              return null;
            }
          }
        }
      }

      // 特别过滤TON钱包扩展的错误模式
      if (event.message || (event.exception && event.exception.values && event.exception.values[0])) {
        const errorMessage = event.message || 
          (event.exception.values[0] && event.exception.values[0].value) || '';
        
        // TON钱包相关错误
        if (
          errorMessage.includes("Cannot read properties of undefined (reading 'ton')") ||
          errorMessage.includes('requestAccounts') ||
          errorMessage.includes('onDappSendUpdates') ||
          errorMessage.includes('Attempting to use a disconnected port object')
        ) {
          return null;
        }
      }

      // 过滤掉来自Microsoft Clarity但实际是扩展错误的情况
      if (event.request && event.request.url && event.request.url.includes('clarity.ms')) {
        // 如果错误消息包含已知的扩展错误模式，则过滤掉
        const errorMessage = event.message || 
          (event.exception && event.exception.values && event.exception.values[0] && event.exception.values[0].value) || '';
        
        if (
          errorMessage.includes('ton') ||
          errorMessage.includes('extensionServiceWorker') ||
          errorMessage.includes('requestAccounts') ||
          errorMessage.includes('onDappSendUpdates')
        ) {
          return null;
        }
      }

      return event;
    },
  });
} else {
  console.log("Sentry is disabled in development mode");
}
